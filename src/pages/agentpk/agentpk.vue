<template>
  <div class="agentpk-fullscreen">
    <!-- 顶部导航栏 -->
    <div class="top-bar">
      <!-- 董会答介绍区域 -->
      <div class="assistant-intro">
        <div class="assistant-avatar">
          <img src="@/assets/assistant/董会答.png" alt="董会答" />
        </div>
        <div class="assistant-info">
          <div class="assistant-name">董会答</div>
          <div class="assistant-desc">懂你懂美团的问答助手</div>
        </div>
        <div class="assistant-actions">
          <div v-if="currentUserName" class="user-greeting">
            你好，{{ currentUserName }}
          </div>
          <button class="action-btn more-assistants" @click="goBack">
            更多助手
          </button>
          <button class="action-btn toggle-memo" @click="toggleMemoSection">
            {{ showMemoSection ? '收起资料' : '展开资料' }}
          </button>
        </div>
      </div>

      <!-- 备忘录组件区域 -->
      <div v-if="showMemoSection" class="memo-section">
        <PersonalMemo
          ref="personalMemoRef"
          :user-id="currentUserId"
          :person-id="currentPersonId"
          @add-person-memo="handleAddPersonMemo"
          @edit-person-memo="handleEditPersonMemo"
          @delete-memo="handleDeleteMemo"
        />
        <IndustryMemo
          ref="industryMemoRef"
          @add-industry-memo="handleAddIndustryMemo"
          @edit-industry-memo="handleEditIndustryMemo"
          @delete-industry-memo="handleDeleteIndustryMemo"
        />
        <CompanyKnowledgeBase @open-knowledge-dialog="handleOpenKnowledgeDialog" />
        <PreInfo
          @add-pre-info="handleAddPreInfo"
        />
      </div>
    </div>

    <!-- 备忘录对话框 -->
    <AddPersonMemo
      v-if="showAddPersonMemo && currentUserId && currentPersonId"
      :user-id="currentUserId"
      :person-id="currentPersonId"
      @close="handleClosePersonMemo"
      @save="handlePersonMemoSave"
    />

    <AddIndustryMemo
      v-if="showAddIndustryMemo && currentUserId"
      :user-id="currentUserId"
      @close="handleCloseIndustryMemo"
      @save="handleIndustryMemoSave"
    />

    <KnowledgeDialog
      v-if="showKnowledgeDialog"
      @close="handleCloseKnowledgeDialog"
    />

    <!-- 主要内容区域 -->
    <div class="main-content" :class="{ 'memo-expanded': showMemoSection }">
      <!-- 聊天内容区域 -->
      <div v-if="chatMessages.length > 0" class="chat-content">
        <div ref="scrollWrapper" class="chat-messages">
          <template v-for="(item, index) in chatMessages" :key="item.key || index">
            <!-- 用户消息和AI回答使用AgentChatItem组件 -->
            <AgentChatItem
              v-if="item.role === 'user' || item.role === 'assistant'"
              :message-data="item"
              :original-question="getOriginalQuestionForMessage(item, index)"
              @send-message="handleSendMessage"
            />

            <!-- 思考过程组件 -->
            <AgentThinkingProcess
              v-if="item.role === 'thinking'"
              :thinking-data="(item as any).thinkingData"
            />
          </template>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="input-section">
        <InputSection
          ref="inputSectionRef"
          :has-messages="chatMessages.length > 0"
          @send-message="handleSendMessage"
          @batch-search="handleBatchSearch"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router';
import { onMounted, onUnmounted, ref, nextTick, onBeforeMount } from 'vue';
import { showFailToast ,showSuccessToast } from 'vant';
import { getUserInfo } from '@/apis/common';
import { getUserProfile } from '@/apis/relation';
import { comprehensiveSearchStream } from './apis/search';
import { getConversationHistory } from './apis/history';
import InputSection from './components/InputSection.vue';
import AgentChatItem from './components/AgentChatItem.vue';
import AgentThinkingProcess from './components/AgentThinkingProcess.vue';
import PersonalMemo from './components/PersonalMemo.vue';
import IndustryMemo from './components/IndustryMemo.vue';
import CompanyKnowledgeBase from './components/CompanyKnowledgeBase.vue';
import PreInfo from './components/PreInfo.vue';
import AddPersonMemo from './components/AddPersonMemo.vue';
import AddIndustryMemo from './components/AddIndustryMemo.vue';
import KnowledgeDialog from './components/KnowledgeDialog.vue';

const router = useRouter();

// 输入框引用
const inputSectionRef = ref<InstanceType<typeof InputSection> | null>(null);

// 滚动容器引用
const scrollWrapper = ref<HTMLElement | null>(null);

// 用户信息
const currentUserId = ref('');
const currentPersonId = ref('');
const currentUserName = ref('');

// 聊天相关状态
const chatMessages = ref<ExtendedChatMessage[]>([]);
const isStoppedByUser = ref(false);
const streamController = ref<AbortController | null>(null);
const canSendMessage = ref(true);



// 定义扩展的消息类型，包含思考过程
type ExtendedChatMessage = IChatStreamContent | {
  role: 'thinking';
  key: string | number;
  thinkingData: {
    items: Array<{
      type: 'status' | 'question' | 'log' | 'search_questions' | 'search_result' | 'core_answer_start' | 'final_answer_complete';
      message?: string;
      step?: 'start' | 'processing' | 'complete';
      question?: string;
      questions?: string[];
      results?: Array<{ title: string; link: string }>;
    }>;
    isLoading: boolean;
  };
};

// 当前思考过程数据（用于实时更新）
const currentThinkingData = ref<{
  items: Array<{
    type: 'status' | 'question' | 'log' | 'search_questions' | 'search_result' | 'core_answer_start' | 'final_answer_complete';
    message?: string;
    step?: 'start' | 'processing' | 'complete';
    question?: string;
    questions?: string[];
    results?: Array<{ title: string; link: string }>;
  }>;
  isLoading: boolean;
}>({
  items: [],
  isLoading: false,
});

// 备忘录相关状态
const showAddPersonMemo = ref(false);
const showAddIndustryMemo = ref(false);
const showKnowledgeDialog = ref(false);
const showMemoSection = ref(true); // 控制备忘录区域的显示/隐藏
const personalMemoRef = ref<InstanceType<typeof PersonalMemo> | null>(null);
const industryMemoRef = ref<InstanceType<typeof IndustryMemo> | null>(null);



// 滚动到底部
const scrollToEnd = () => {
  if (scrollWrapper.value) {
    scrollWrapper.value.scrollTop = scrollWrapper.value.scrollHeight;
  }
};

// 处理状态消息和日志消息中的时间显示，将"[0秒]"、"[0.0秒]"、"[0.00秒]"替换为"[0.01秒]"
const processStatusMessage = (message: string): string => {
  if (!message) return message;
  // 使用正则表达式检测并替换被方括号包围的"[0秒]"、"[0.0秒]"、"[0.00秒]"为"[0.01秒]"
  return message.replace(/\[0(?:\.0{1,2})?秒\]/g, '[0.01秒]');
};

// 获取消息对应的原始问题
const getOriginalQuestionForMessage = (messageItem: IChatStreamContent, index: number): string | undefined => {
  // 如果是助手消息，向前查找最近的用户消息
  if (messageItem.role === 'assistant') {
    for (let i = index - 1; i >= 0; i--) {
      const prevMessage = chatMessages.value[i];
      if (prevMessage && prevMessage.role === 'user') {
        return prevMessage.content;
      }
    }
  }
  return undefined;
};

// 处理发送消息
const handleSendMessage = async (message: string) => {
  console.log('🔄 [agentpk] 发送消息:', message);

  if (!message.trim()) return;

  // 检查是否可以发送消息（打字机工作期间禁止发送）
  if (!canSendMessage.value) {
    console.log('🚫 [agentpk] 打字机正在工作，禁止发送新消息');
    showFailToast('请等待当前回复完成后再发送消息');
    return;
  }

  // 检查用户信息是否已加载
  if (!currentUserId.value || currentUserId.value === 'unknown_user') {
    showFailToast('用户信息未加载，请刷新页面重试');
    return;
  }

  await sendSearchMessage(message);
};

// 处理批量搜索
const handleBatchSearch = async (message: string) => {
  console.log('🔄 [agentpk] 批量搜索:', message);

  if (!message.trim()) return;

  // 先发送消息执行搜索
  await sendSearchMessage(message);

  // 然后跳转到ThreeInOne页面，并携带查询参数
  router.push({
    path: '/agentpk/three-in-one',
    query: { q: message }
  });
};

// 发送搜索消息
const sendSearchMessage = async (messageContent: string) => {
  if (!messageContent.trim() || !currentUserId.value) {
    return;
  }

  console.log('🚀 [agentpk] 开始发送搜索消息:', messageContent);

  // 如果有正在进行的请求，先取消它
  if (streamController.value) {
    console.log('🔄 [agentpk] 取消正在进行的请求');
    streamController.value.abort();
    streamController.value = null;
  }

  // 重置状态
  isStoppedByUser.value = false;
  canSendMessage.value = false;

  // 禁用输入框
  if (inputSectionRef.value) {
    inputSectionRef.value.setLoading(true);
  }

  // 清空当前思考数据
  currentThinkingData.value.items = [];
  currentThinkingData.value.isLoading = false;

  // 添加用户消息
  const userMessage: IChatStreamContent = {
    role: 'user',
    content: messageContent,
    key: Date.now(),
    isFinish: true,
    reasoningData: { content: '', status: '' },
  };
  chatMessages.value.push(userMessage);

  // 添加思考过程占位符 - 使用特殊的消息类型，每个消息都有独立的思考数据
  const thinkingKey = Date.now() + 1;
  const thinkingMessage: ExtendedChatMessage = {
    role: 'thinking' as const,
    key: thinkingKey,
    thinkingData: {
      items: [] as Array<{
        type: 'status' | 'question' | 'log' | 'search_questions' | 'search_result' | 'core_answer_start' | 'final_answer_complete';
        message?: string;
        step?: 'start' | 'processing' | 'complete';
        question?: string;
        questions?: string[];
        results?: Array<{ title: string; link: string }>;
      }>,
      isLoading: false,
    },
  };
  chatMessages.value.push(thinkingMessage);

  // 保存当前思考消息的引用，用于后续更新
  const currentThinkingMessageRef = thinkingMessage;

  // 添加助手消息占位符
  const assistantMessage: IChatStreamContent = {
    role: 'assistant',
    content: '',
    key: Date.now() + 2,
    isFinish: false,
    reasoningData: { content: '', status: '' },
  };
  chatMessages.value.push(assistantMessage);

  // 滚动到底部
  await nextTick(() => {
    scrollToEnd();
  });

  // 创建新的AbortController
  streamController.value = new AbortController();

  try {
    // 开始综合搜索
    await comprehensiveSearchStream(
      {
        question: messageContent,
        user_id: currentUserId.value,
        conversation_id: 'search_chat',
      },
      {
        onStatus: (data) => {
          console.log('📊 [agentpk] 状态更新:', data);
          // 创建新的思考数据对象来触发响应式更新
          const newThinkingData = {
            ...currentThinkingMessageRef.thinkingData,
            items: [
              ...currentThinkingMessageRef.thinkingData.items,
              {
                type: 'status' as const,
                message: processStatusMessage(data.message),
                step: data.step,
              }
            ],
            isLoading: (() => {
              if (data.step === 'start') return true;
              if (data.step === 'complete') return false;
              return currentThinkingMessageRef.thinkingData.isLoading;
            })()
          };
          currentThinkingMessageRef.thinkingData = newThinkingData;
          // 强制触发Vue响应式更新
          chatMessages.value = [...chatMessages.value];
        },
        onQuestion: (data) => {
          console.log('❓ [agentpk] 问题:', data);
          // 创建新的思考数据对象来触发响应式更新
          const newThinkingData = {
            ...currentThinkingMessageRef.thinkingData,
            items: [
              ...currentThinkingMessageRef.thinkingData.items,
              {
                type: 'question' as const,
                question: data.question,
              }
            ]
          };
          currentThinkingMessageRef.thinkingData = newThinkingData;
          // 强制触发Vue响应式更新
          chatMessages.value = [...chatMessages.value];
        },
        onLog: (data) => {
          console.log('📝 [agentpk] 日志:', data);
          // 创建新的思考数据对象来触发响应式更新
          const newThinkingData = {
            ...currentThinkingMessageRef.thinkingData,
            items: [
              ...currentThinkingMessageRef.thinkingData.items,
              {
                type: 'log' as const,
                message: processStatusMessage(data.message),
              }
            ]
          };
          currentThinkingMessageRef.thinkingData = newThinkingData;
          // 强制触发Vue响应式更新
          chatMessages.value = [...chatMessages.value];
        },
        onKnowledgeContext: (data) => {
          console.log('📚 [agentpk] 知识上下文:', data);
          // 暂时不展示
        },
        onSearchQuestions: (data) => {
          console.log('🔍 [agentpk] 搜索问题:', data);
          // 创建新的思考数据对象来触发响应式更新
          const newThinkingData = {
            ...currentThinkingMessageRef.thinkingData,
            items: [
              ...currentThinkingMessageRef.thinkingData.items,
              {
                type: 'search_questions' as const,
                questions: data.questions,
              }
            ]
          };
          currentThinkingMessageRef.thinkingData = newThinkingData;
          // 强制触发Vue响应式更新
          chatMessages.value = [...chatMessages.value];
        },
        onSearchResult: (data) => {
          console.log('📋 [agentpk] 搜索结果:', data);
          const results = data.results.map(result => ({
            title: result.title,
            link: result.link,
          }));
          // 创建新的思考数据对象来触发响应式更新
          const newThinkingData = {
            ...currentThinkingMessageRef.thinkingData,
            items: [
              ...currentThinkingMessageRef.thinkingData.items,
              {
                type: 'search_result' as const,
                results,
              }
            ]
          };
          currentThinkingMessageRef.thinkingData = newThinkingData;
          // 强制触发Vue响应式更新
          chatMessages.value = [...chatMessages.value];
        },
        onCoreAnswerStart: (data) => {
          console.log('🚀 [agentpk] 核心答案开始:', data);
          // 创建新的思考数据对象来触发响应式更新
          const newThinkingData = {
            ...currentThinkingMessageRef.thinkingData,
            items: [
              ...currentThinkingMessageRef.thinkingData.items,
              {
                type: 'core_answer_start' as const,
              }
            ]
          };
          currentThinkingMessageRef.thinkingData = newThinkingData;
          // 强制触发Vue响应式更新
          chatMessages.value = [...chatMessages.value];
        },
        onFinalAnswer: (data) => {
          console.log('✅ [agentpk] 最终答案:', data);
          // 添加 final_answer_complete 事件标记并完成思考过程
          const newThinkingData = {
            ...currentThinkingMessageRef.thinkingData,
            items: [
              ...currentThinkingMessageRef.thinkingData.items,
              {
                type: 'final_answer_complete' as const,
              }
            ],
            isLoading: false
          };
          currentThinkingMessageRef.thinkingData = newThinkingData;
          // 强制触发Vue响应式更新
          chatMessages.value = [...chatMessages.value];

          // 构建完整答案
          const coreAnswer = data.core_answer || '';
          const detailedAnswer = data.detailed_answer || '';
          const fullAnswer = [coreAnswer, detailedAnswer].filter(Boolean).join('\n\n');

          // 直接更新最后一条助手消息的内容，让AgentChatItem组件处理打字机效果
          if (chatMessages.value.length > 0) {
            const lastMessage = chatMessages.value[chatMessages.value.length - 1];
            if (lastMessage && lastMessage.role === 'assistant') {
              lastMessage.content = fullAnswer;
              lastMessage.isFinish = true;
            }
          }

          // 重新启用输入框
          canSendMessage.value = true;
          streamController.value = null;
          if (inputSectionRef.value) {
            inputSectionRef.value.setLoading(false);
          }
        },
        onError: (error) => {
          console.error('❌ [agentpk] 搜索错误:', error);
          canSendMessage.value = true;
          streamController.value = null;
          if (inputSectionRef.value) {
            inputSectionRef.value.setLoading(false);
          }
        },
        onClose: () => {
          console.log('🏁 [agentpk] 搜索连接关闭');
        },
      },
      streamController.value.signal,
    );
  } catch (error) {
    console.error('❌ [agentpk] 发送搜索消息失败:', error);
    canSendMessage.value = true;
    streamController.value = null;
    if (inputSectionRef.value) {
      inputSectionRef.value.setLoading(false);
    }
  }
};

// 备忘录相关事件处理函数
const handleAddPersonMemo = () => {
  console.log('🔄 [agentpk] 打开添加个人备忘录对话框');
  console.log('🔍 [agentpk] 当前用户数据:', {
    currentUserId: currentUserId.value,
    currentPersonId: currentPersonId.value,
    showAddPersonMemo: showAddPersonMemo.value
  });
  showAddPersonMemo.value = true;
  console.log('🔍 [agentpk] 设置后的状态:', {
    showAddPersonMemo: showAddPersonMemo.value,
    条件检查: !!(showAddPersonMemo.value && currentUserId.value && currentPersonId.value)
  });
};

const handleEditPersonMemo = (event: unknown) => {
  console.log('🔄 [agentpk] 编辑个人备忘录:', event);
  // TODO: 实现编辑功能
};

const handleDeleteMemo = (memory: unknown) => {
  console.log('🔄 [agentpk] 删除备忘录:', memory);
  // TODO: 实现删除功能
};

const handleAddIndustryMemo = () => {
  console.log('🔄 [agentpk] 打开添加行业备忘录对话框');
  console.log('🔍 [agentpk] 当前用户数据:', {
    currentUserId: currentUserId.value,
    showAddIndustryMemo: showAddIndustryMemo.value
  });
  showAddIndustryMemo.value = true;
  console.log('🔍 [agentpk] 设置后的状态:', {
    showAddIndustryMemo: showAddIndustryMemo.value,
    条件检查: showAddIndustryMemo.value && currentUserId.value
  });
};

const handleAddPreInfo = () => {
  console.log('🔄 [agentpk] 预获取资讯按钮被点击');
  // TODO: 实现预获取资讯功能
};

const handlePersonMemoSave = (content: string) => {
  console.log('🔄 [agentpk] 个人备忘录保存成功:', content);
  showAddPersonMemo.value = false;
  // 刷新个人备忘录列表
  if (personalMemoRef.value) {
    personalMemoRef.value.fetchMemories();
  }
};

const handleIndustryMemoSave = (content: string) => {
  console.log('🔄 [agentpk] 行业备忘录保存成功:', content);
  showAddIndustryMemo.value = false;
  // 刷新行业备忘录列表
  if (industryMemoRef.value) {
    industryMemoRef.value.fetchIndustryMemos();
  }
};

const handleEditIndustryMemo = (memo: { id: string; content: string; created_at: string; updated_at?: string }) => {
  console.log('🔄 [agentpk] 编辑行业备忘录:', memo);
  // TODO: 实现编辑行业备忘录功能
};

const handleDeleteIndustryMemo = (memo: { id: string; content: string; created_at: string; updated_at?: string }) => {
  console.log('🔄 [agentpk] 删除行业备忘录:', memo);
  // TODO: 实现删除行业备忘录功能
};

const handleClosePersonMemo = () => {
  showAddPersonMemo.value = false;
};

const handleCloseIndustryMemo = () => {
  showAddIndustryMemo.value = false;
};

const handleOpenKnowledgeDialog = () => {
  console.log('🔄 [agentpk] 打开知识库对话框');
  showKnowledgeDialog.value = true;
};

const handleCloseKnowledgeDialog = () => {
  showKnowledgeDialog.value = false;
};

// 切换备忘录区域显示/隐藏
const toggleMemoSection = () => {
  showMemoSection.value = !showMemoSection.value;
  console.log('🔄 [agentpk] 切换备忘录区域显示状态:', showMemoSection.value);
};

// 获取用户信息
const loadUserInfo = async () => {
  try {
    console.log('🔄 [agentpk] 开始获取用户信息...');
    const userInfo = await getUserInfo();
    console.log('📡 [agentpk] 用户信息:', userInfo);

    if (userInfo && userInfo.login) {
      currentUserId.value = userInfo.login;
      console.log('✅ [agentpk] 用户信息加载成功, userId:', currentUserId.value);
    } else {
      console.warn('⚠️ [agentpk] 用户信息格式异常');
      // 设置默认值，避免连接失败
      currentUserId.value = 'unknown_user';
    }
  } catch (error) {
    console.error('❌ [agentpk] 获取用户信息失败:', error);
    currentUserId.value = 'unknown_user';
  }
};

// 返回首页
const goBack = async () => {
  console.log('🔙 [agentpk] 返回首页');
  await router.push({
    name: 'chat', // 返回到index.vue (chat路由)
  });
};

// 设置全屏样式
const setFullscreenStyles = () => {
  // 添加全屏样式类
  document.documentElement.classList.add('agentpk-active');
  document.body.classList.add('agentpk-active');
  const app = document.getElementById('app');
  if (app) {
    app.classList.add('agentpk-active');
  }

  console.log('🎨 [agentpk] 已应用全屏样式');
};

// 移除全屏样式
const removeFullscreenStyles = () => {
  document.documentElement.classList.remove('agentpk-active');
  document.body.classList.remove('agentpk-active');
  const app = document.getElementById('app');
  if (app) {
    app.classList.remove('agentpk-active');
  }

  console.log('🎨 [agentpk] 已移除全屏样式');
};

// 获取用户档案信息
const loadUserProfile = async () => {
  if (currentUserId.value && currentUserId.value !== 'unknown_user') {
    try {
      const userProfile = await getUserProfile({
        user_id: currentUserId.value,
      });

      if (userProfile && userProfile.result === 'success' && userProfile.person) {
        currentPersonId.value = userProfile.person.person_id;
        currentUserName.value = userProfile.person.canonical_name || '';
        console.log('👤 [agentpk] 用户信息获取成功:', {
          person_id: currentPersonId.value,
          name: currentUserName.value
        });
      } else {
        console.warn('⚠️ [agentpk] 获取用户 person_id 失败:', userProfile);
        currentPersonId.value = currentUserId.value; // 降级使用 user_id
        currentUserName.value = ''; // 清空姓名
      }
    } catch (profileError) {
      console.error('❌ [agentpk] 获取用户档案失败:', profileError);
      currentPersonId.value = currentUserId.value; // 降级使用 user_id
      currentUserName.value = ''; // 清空姓名
    }
  }
};

// 恢复历史对话
const loadHistoryConversation = async () => {
  if (!currentUserId.value) {
    console.warn('⚠️ [agentpk] 用户ID不存在，无法恢复历史对话');
    return;
  }

  try {
    console.log('🔄 [agentpk] 开始恢复历史对话...');
    const historyResponse = await getConversationHistory('search_chat', currentUserId.value);

    if (historyResponse.status === 'success' && historyResponse.history && historyResponse.history.length > 0) {
      console.log('📚 [agentpk] 恢复历史对话成功，消息数量:', historyResponse.history.length);

      // 清空当前消息列表
      chatMessages.value = [];

      // 将历史消息转换为聊天消息格式
      historyResponse.history.forEach((message, index) => {
        if (message.type === 'human') {
          // 用户消息
          const userMessage: IChatStreamContent = {
            role: 'user',
            content: message.content,
            key: Date.now() + index * 2,
            isFinish: true,
            reasoningData: { content: '', status: '' },
          };
          chatMessages.value.push(userMessage);
        } else if (message.type === 'ai') {
          // AI历史消息：只添加助手回复，不添加思考过程
          const assistantMessage: IChatStreamContent = {
            role: 'assistant',
            content: message.content,
            key: Date.now() + index * 2 + 1,
            isFinish: true,
            reasoningData: { content: '', status: '' },
          };
          chatMessages.value.push(assistantMessage);
        }
      });

      // 滚动到底部
      await nextTick(() => {
        scrollToEnd();
      });

      console.log('✅ [agentpk] 历史对话恢复完成');
    } else {
      console.log('📝 [agentpk] 没有找到历史对话记录');
    }
  } catch (error) {
    console.error('❌ [agentpk] 恢复历史对话失败:', error);
  }
};

// 页面挂载前的初始化
onBeforeMount(async () => {
  // 先加载用户信息
  await loadUserInfo();
  // 然后加载用户档案
  await loadUserProfile();
  // 最后恢复历史对话
  await loadHistoryConversation();
});

// 页面挂载时的初始化
onMounted(() => {
  console.log('🚀 [agentpk] 全屏页面加载完成');
  setFullscreenStyles();

  // 初始化完成
  console.log('✅ [agentpk] 页面初始化完成');
});

// 组件卸载时的清理工作
onUnmounted(() => {
  console.log('🔚 [agentpk] 页面卸载，恢复原始样式');
  removeFullscreenStyles();

  // 如果有正在进行的请求，取消它
  if (streamController.value) {
    streamController.value.abort();
    streamController.value = null;
  }

  // 清理完成
  console.log('✅ [agentpk] 组件清理完成');
});
</script>

<style lang="scss" scoped>
.agentpk-fullscreen {
  // 占满整个浏览器窗口，不受H5布局限制
  position: relative;
  top: 0;
  left: 0;
  height: 100vh;
  width: 100%;
  max-width: 100%;
  background: linear-gradient(135deg, #ffffff 0%, #faf9ff 50%, #f8f6ff 100%);
  z-index: 9999;
  overflow: hidden; // 禁止整体滚动
  display: flex;
  flex-direction: column;

  // 确保不受父容器样式影响
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.top-bar {
  position: relative;
  width: 100%;
  // min-height: 120px; // 最小高度，可以根据内容动态调整
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(139, 126, 216, 0.2);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 10;
  flex-shrink: 0; // 防止被压缩

  // 董会答介绍区域
  .assistant-intro {
    display: flex;
    align-items: center;
    gap: 6px;
    width: 100%;
    background: #f3f9ff; // 更浅的蓝色背景
    // border: 2px solid  #93cdfd; // 蓝色边框
    // border-radius: 12px;
    padding: 6px;

    .assistant-avatar {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      overflow: hidden;
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .assistant-info {
      flex: 1;

      .assistant-name {
        font-size: 28px;
        font-weight: 600;
        color: hsl(238, 57%, 25%);
      }

      .assistant-desc {
        font-size: 22px;
        color: #333;
      }
    }

    .assistant-actions {
      display: flex;
      align-items: center;
      gap: 6px;

      .user-greeting {
        font-size: 24px;
        font-weight: 500;
        color: hsl(238, 57%, 25%);
        margin-right: 8px;
      }

      .action-btn {
        padding: 8px 8px;
        border: none;
        color: hsl(238, 57%, 25%);
        font-size: 24px;
        background-color: transparent;
        cursor: pointer;
        transition: all 0.3s ease;

        &.back-btn {
          display: flex;
          align-items: center;
          gap: 8px;
          background: rgba(139, 126, 216, 0.1);
          border: 1px solid rgba(139, 126, 216, 0.3);
          border-radius: 8px;
          font-weight: 500;
          backdrop-filter: blur(10px);

          &:hover {
            background: rgba(139, 126, 216, 0.2);
            border-color: rgba(139, 126, 216, 0.5);
            transform: translateY(-1px);
          }

          &:active {
            transform: translateY(0);
          }

          svg {
            width: 24px;
            height: 24px;
          }
        }
      }
    }
  }

  // 备忘录组件区域 - 上下排布
  .memo-section {
    display: flex;
    flex-direction: column;
    width: 100%;
  }
}

.top-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 20px;
  font-weight: bold;
  color: #333;
  text-align: center;
}

.top-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 20px;
  font-weight: bold;
  color: #333;
  text-align: center;
}



.main-content {
  position: relative;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px 120px 40px; // 底部留出空间给固定的input-section
  flex: 1;
  min-height: 0; // 确保flex子元素可以正确计算高度


  // 动态调整 padding-top，根据备忘录区域是否展开
  padding-top: 0px; // 基础高度（董会答区域）

  // 当备忘录展开时，增加额外的空间
  &.memo-expanded {
    padding-top: 0px; // 展开时的总高度（增加了获取预资讯组件）
  }

  // 移除动态底部间距，因为input-section不再固定在底部

  .chat-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .chat-messages {
      flex: 1;
      overflow-y: auto;
      padding: 20px 0;
      scroll-behavior: smooth;

      // 自定义滚动条样式
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(139, 126, 216, 0.1);
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(139, 126, 216, 0.3);
        border-radius: 3px;
        transition: background 0.3s ease;

        &:hover {
          background: rgba(139, 126, 216, 0.5);
        }
      }
    }
  }
}

.input-section {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  padding: 20px 40px;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
}
</style>

<style>
/* 全局样式，确保页面真正占满全屏 */
body.agentpk-active {
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
}

html.agentpk-active {
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
}

#app.agentpk-active {
  overflow: hidden !important;
}

/* 响应式适配 - 确保在不同屏幕尺寸下正常显示 */
@media (max-width: 768px) {
  .agentpk-fullscreen {
    width: 100% !important;
    max-width: 100% !important;
    min-width: 320px !important;
  }

  .main-content {
    padding: 0 20px 120px 20px !important; /* 底部留出空间给固定的input-section */
    max-width: 100% !important;

    &.memo-expanded {
      padding-top: 0px !important; /* 减少顶部padding */
    }

    &:not(.memo-expanded) {
      padding-top: 0px !important; /* 减少顶部padding */
    }
  }

  .top-bar {
    padding: 0px !important;
  }

  .assistant-intro {
    padding: 2px !important;
    gap: 4px !important;
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;

    .assistant-avatar {
      width: 45px !important;
      height: 45px !important;
      flex-shrink: 0 !important;
    }

    .assistant-info {
      flex: 1 !important;
      min-width: 0 !important;

      .assistant-name {
        font-size: 16px !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
      }

      .assistant-desc {
        font-size: 12px !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
      }
    }

    .assistant-actions {
      gap: 3px !important;
      flex-shrink: 0 !important;
      display: flex !important;
      flex-direction: row !important;
      align-items: center !important;
      flex-wrap: nowrap !important;

      .user-greeting {
        font-size: 12px !important;
        margin-right: 2px !important;
        white-space: nowrap !important;
      }

      .action-btn {
        font-size: 12px !important;
        padding: 2px 4px !important;
        border-radius: 6px !important;
        background: transparent !important;
        border: none !important;
        white-space: nowrap !important;
        min-width: auto !important;
      }
    }
  }

  .input-section {
    padding: 20px !important;

    /* 确保输入组件在移动端正常显示 */
    :deep(.input-container) {
      max-width: 100% !important;
    }

    :deep(.input-wrapper) {
      padding: 0 16px !important;
    }
  }



  /* 批量测评模式的响应式适配 */
  .chat-messages.batch-mode {
    .batch-responses {
      flex-direction: column !important;
      gap: 16px !important;

      .response-column {
        width: 100% !important;
        min-width: auto !important;
      }
    }

    .batch-user-message {
      margin-bottom: 20px !important;

      .agent-chat-message {
        max-width: 100% !important;
      }
    }
  }

  /* 备忘录区域的响应式适配 */
  .memo-section {
    gap: 8px !important;
  }
}
</style>
