<template>
  <div class="thinking-progress-checklist">
    <div class="progress-items">
      <div
        v-for="stage in stages"
        :key="stage.key"
        class="progress-item"
        :class="{ 'completed': stage.completed, 'current': stage.current }"
      >
        <!-- 步骤图标圆形 -->
        <div class="progress-circle" :style="{ backgroundColor: stage.color }">
          <div class="circle-icon">
            <img v-if="stage.icon" :src="stage.icon" :alt="stage.label" />
            <div v-else class="default-icon">{{ stage.emoji }}</div>
          </div>

          <!-- 完成状态的对勾 - 右下角 -->
          <div v-if="stage.completed" class="check-mark">
            <svg width="12" height="12" viewBox="0 0 24 24" fill="none">
              <path d="M20 6L9 17L4 12" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>

          <!-- 当前进行中的加载动画 -->
          <div v-else-if="stage.current" class="loading-ring"></div>
        </div>

        <!-- 步骤标签 -->
        <div class="step-label">{{ stage.label }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface IThinkingItem {
  type: 'status' | 'question' | 'log' | 'search_questions' | 'search_result' | 'core_answer_start' | 'final_answer_complete';
  message?: string;
  step?: 'start' | 'processing' | 'complete';
  question?: string;
  questions?: string[];
  results?: Array<{ title: string; link: string }>;
}

interface IThinkingData {
  items: IThinkingItem[];
  isLoading: boolean;
}

interface IProps {
  thinkingData: IThinkingData;
  isComplete?: boolean;
}

const props = defineProps<IProps>();

// 定义五个阶段
const stages = computed(() => {
  const {items} = props.thinkingData;
  const hasQuestion = items.some(item => item.type === 'question');
  const hasSearchQuestions = items.some(item => item.type === 'search_questions');
  const hasSearchResult = items.some(item => item.type === 'search_result');
  const hasCoreAnswerStart = items.some(item => item.type === 'core_answer_start');
  const hasFinalAnswerComplete = items.some(item => item.type === 'final_answer_complete');

  return [
    {
      key: 'analyze',
      label: '分析',
      completed: hasQuestion,
      current: !hasQuestion && props.thinkingData.isLoading,
      color: '#FF9500', // 橙色
      emoji: '⚖️', // 天平图标
      icon: null
    },
    {
      key: 'search',
      label: '搜索',
      completed: hasSearchQuestions,
      current: hasQuestion && !hasSearchQuestions && props.thinkingData.isLoading,
      color: '#007AFF', // 蓝色
      emoji: '�', // 放大镜图标
      icon: null
    },
    {
      key: 'summarize',
      label: '汇总',
      completed: hasSearchResult,
      current: hasSearchQuestions && !hasSearchResult && props.thinkingData.isLoading,
      color: '#8B7ED8', // 紫色
      emoji: '🎯', // 同心圆目标图标
      icon: null
    },
    {
      key: 'synthesize',
      label: '合成',
      completed: hasCoreAnswerStart,
      current: hasSearchResult && !hasCoreAnswerStart && props.thinkingData.isLoading,
      color: '#34C759', // 绿色
      emoji: '⚙️', // 保持齿轮图标
      icon: null
    },
    {
      key: 'complete',
      label: '完成',
      completed: hasFinalAnswerComplete,
      current: hasCoreAnswerStart && !hasFinalAnswerComplete && props.thinkingData.isLoading,
      color: '#10B981', // 成功绿色
      emoji: '🏁', // 旗帜图标
      icon: null
    }
  ];
});
</script>

<style lang="scss" scoped>
.thinking-progress-checklist {
  width: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 8px;
  align-self: flex-end; // 向下对齐

  .progress-items {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: center;

    .progress-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
      transition: all 0.3s ease;

      // 默认状态 - 半透明
      opacity: 0.4;

      // 当前进行中状态
      &.current {
        opacity: 1;

        .progress-circle {
          transform: scale(1.1);
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
          animation: pulse 2s infinite;
        }

        .step-label {
          color: var(--primary-color, #8B7ED8);
          font-weight: 600;
        }
      }

      // 已完成状态
      &.completed {
        opacity: 1;

        .step-label {
          color: var(--success-color, #10B981);
          font-weight: 500;
        }
      }

      .progress-circle {
        position: relative;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .circle-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: 100%;

          img {
            width: 20px;
            height: 20px;
            filter: brightness(0) invert(1); // 白色图标
          }

          .default-icon {
            font-size: 24px;
            filter: brightness(0) invert(1); // 白色表情符号
          }
        }

        // 完成状态的对勾 - 右下角
        .check-mark {
          position: absolute;
          bottom: -2px;
          right: -2px;
          width: 16px;
          height: 16px;
          background: #10B981;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
          border: 2px solid white;
        }

        // 当前进行中的加载环
        .loading-ring {
          position: absolute;
          top: -2px;
          left: -2px;
          right: -2px;
          bottom: -2px;
          border: 2px solid transparent;
          border-top: 2px solid rgba(255, 255, 255, 0.8);
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
      }

      .step-label {
        font-size: 18px;
        color: var(--text-secondary, #666);
        text-align: center;
        transition: all 0.3s ease;
        white-space: nowrap;
      }
    }
  }
}

// 脉冲动画
@keyframes pulse {
  0%, 100% {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  }
  50% {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
  }
}

// 旋转动画
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
