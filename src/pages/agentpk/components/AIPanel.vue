<template>
  <div class="ai-panel">
    <!-- AI面板标题 -->
    <div class="panel-header">
      <div class="panel-title">{{ title }}</div>
    </div>

    <!-- 消息显示区域 -->
    <div class="messages-container" ref="messagesContainer">
      <!-- 历史消息 -->
      <template v-for="message in messages" :key="message.id">
        <!-- 用户消息 -->
        <div v-if="message.type === 'user'" class="message-item user-message">
          <div class="message-content">
            <div class="message-text">{{ message.content }}</div>
          </div>
        </div>

        <!-- AI消息 -->
        <div v-else-if="message.type === 'assistant'" class="message-item assistant-message">
          <!-- AI思考过程（历史） -->
          <div v-if="message.thinkingData && message.thinkingData.items.length > 0" class="thinking-section">
            <AgentThinkingProcess :thinking-data="message.thinkingData" />
          </div>
          <!-- AI回答 -->
          <div class="message-content">
            <AgentMessageRender :text="message.content" />
          </div>
        </div>
      </template>

      <!-- 当前思考过程（正在进行的） -->
      <div v-if="currentThinkingData.items.length > 0" class="message-item thinking-message">
        <AgentThinkingProcess :thinking-data="currentThinkingData" />
      </div>

      <!-- 加载状态 -->
      <div v-if="isLoading" class="message-item loading-message">
        <div class="loading-dots">
          <span class="dot dot1">.</span>
          <span class="dot dot2">.</span>
          <span class="dot dot3">.</span>
        </div>
      </div>
    </div>

    <!-- 子输入区域 -->
    <SubInputSection
      ref="subInputRef"
      @send-message="handleSubMessage"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, watch } from 'vue';
import AgentThinkingProcess from './AgentThinkingProcess.vue';
import AgentMessageRender from './AgentMessageRender.vue';
import SubInputSection from './SubInputSection.vue';

interface IThinkingItem {
  type: 'status' | 'question' | 'log' | 'search_questions' | 'search_result' | 'core_answer_start' | 'final_answer_complete';
  message?: string;
  step?: 'start' | 'processing' | 'complete';
  question?: string;
  questions?: string[];
  results?: Array<{ title: string; link: string }>;
}

interface IThinkingData {
  items: IThinkingItem[];
  isLoading: boolean;
}

interface IMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: number;
  thinkingData?: IThinkingData; // 助手消息包含完整的思考过程
}

interface IProps {
  title: string;
  messages?: IMessage[];
  currentThinkingData?: IThinkingData;
  isLoading?: boolean;
}

const props = withDefaults(defineProps<IProps>(), {
  messages: () => [],
  currentThinkingData: () => ({ items: [], isLoading: false }),
  isLoading: false,
});

const emit = defineEmits(['sub-message']);

const messagesContainer = ref<HTMLElement | null>(null);
const subInputRef = ref<InstanceType<typeof SubInputSection> | null>(null);

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
    }
  });
};

// 处理子输入框的消息
const handleSubMessage = (message: string) => {
  console.log('🚀 [AIPanel] 收到子输入框消息:', message);
  emit('sub-message', message);
};

// 监听消息变化，自动滚动到底部
watch(
  () => [props.messages.length, props.currentThinkingData.items.length, props.isLoading],
  () => {
    scrollToBottom();
  },
  { deep: true }
);

// 暴露方法给父组件
defineExpose({
  scrollToBottom,
  subInputRef,
});
</script>

<style lang="scss" scoped>
.ai-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: rgba(255, 255, 255, 0.95);
  border: 2px solid rgba(139, 126, 216, 0.3);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, rgba(139, 126, 216, 0.1) 0%, rgba(183, 148, 246, 0.1) 100%);
  border-bottom: 1px solid rgba(139, 126, 216, 0.2);

  .panel-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.message-item {
  &.user-message {
    align-self: flex-end;
    max-width: 80%;

    .message-content {
      background: linear-gradient(135deg, #8B7ED8 0%, #B794F6 100%);
      color: white;
      padding: 12px 16px;
      border-radius: 18px 18px 4px 18px;
      box-shadow: 0 2px 8px rgba(139, 126, 216, 0.3);

      .message-text {
        font-size: 16px;
        line-height: 1.5;
      }
    }
  }

  &.assistant-message {
    align-self: flex-start;
    max-width: 90%;

    .message-content {
      background: rgba(248, 249, 250, 0.9);
      border: 1px solid rgba(139, 126, 216, 0.2);
      padding: 16px;
      border-radius: 18px 18px 18px 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  &.thinking-message {
    align-self: flex-start;
    width: 100%;
  }

  .thinking-section {
    margin-bottom: 8px;
  }

  &.loading-message {
    align-self: flex-start;
    padding: 16px;
  }
}

.loading-dots {
  display: flex;
  gap: 4px;
  align-items: center;

  .dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #8B7ED8;
    animation: loading-bounce 1.4s ease-in-out infinite both;

    &.dot1 {
      animation-delay: -0.32s;
    }

    &.dot2 {
      animation-delay: -0.16s;
    }

    &.dot3 {
      animation-delay: 0s;
    }
  }
}

@keyframes loading-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* 滚动条样式 */
.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: rgba(139, 126, 216, 0.1);
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb {
  background: rgba(139, 126, 216, 0.3);
  border-radius: 3px;

  &:hover {
    background: rgba(139, 126, 216, 0.5);
  }
}
</style>
