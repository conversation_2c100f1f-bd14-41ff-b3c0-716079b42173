<template>
  <div class="three-in-one-fullscreen">
    <!-- 顶部导航栏 -->
    <div class="top-bar">
      <!-- 董会答介绍区域 -->
      <div class="assistant-intro">
        <div class="assistant-avatar">
          <img src="@/assets/assistant/董会答.png" alt="董会答" />
        </div>
        <div class="assistant-info">
          <div class="assistant-name">董会答</div>
          <div class="assistant-desc">懂你懂美团的问答助手</div>
        </div>
        <div class="assistant-actions">
          <div v-if="currentUserName" class="user-greeting">
            你好，{{ currentUserName }}
          </div>
          <button class="action-btn more-assistants" @click="goBack">
            返回
          </button>
          <button class="action-btn toggle-memo" @click="toggleMemoSection">
            {{ showMemoSection ? '收起资料' : '展开资料' }}
          </button>
        </div>
      </div>

      <!-- 备忘录组件区域 -->
      <div v-if="showMemoSection" class="memo-section">
        <PersonalMemo
          ref="personalMemoRef"
          :user-id="currentUserId"
          :person-id="currentPersonId"
          @add-person-memo="handleAddPersonMemo"
          @edit-person-memo="handleEditPersonMemo"
          @delete-memo="handleDeleteMemo"
        />
        <IndustryMemo
          ref="industryMemoRef"
          @add-industry-memo="handleAddIndustryMemo"
          @edit-industry-memo="handleEditIndustryMemo"
          @delete-industry-memo="handleDeleteIndustryMemo"
        />
        <CompanyKnowledgeBase @open-knowledge-dialog="handleOpenKnowledgeDialog" />
        <PreInfo
          @add-pre-info="handleAddPreInfo"
        />
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content" :class="{ 'memo-expanded': showMemoSection }">
      <!-- AI比较区域 -->
      <div class="ai-comparison-section">
        <AIPanel
          ref="aiPanel1Ref"
          title="AI助手 1"
          :messages="panel1Data.messages"
          :current-thinking-data="panel1Data.currentThinkingData"
          :is-loading="panel1Data.isLoading"
          @sub-message="(message) => handleSubMessage(message, 1)"
        />
        <AIPanel
          ref="aiPanel2Ref"
          title="AI助手 2"
          :messages="panel2Data.messages"
          :current-thinking-data="panel2Data.currentThinkingData"
          :is-loading="panel2Data.isLoading"
          @sub-message="(message) => handleSubMessage(message, 2)"
        />
        <AIPanel
          ref="aiPanel3Ref"
          title="AI助手 3"
          :messages="panel3Data.messages"
          :current-thinking-data="panel3Data.currentThinkingData"
          :is-loading="panel3Data.isLoading"
          @sub-message="(message) => handleSubMessage(message, 3)"
        />
      </div>

      <!-- 输入区域 -->
      <div class="input-section">
        <InputSection
          ref="inputSectionRef"
          :has-messages="false"
          @send-message="handleSendMessage"
          @batch-search="handleBatchSearch"
        />
      </div>
    </div>

    <!-- 备忘录对话框 -->
    <AddPersonMemo
      v-if="showAddPersonMemo && currentUserId && currentPersonId"
      :user-id="currentUserId"
      :person-id="currentPersonId"
      @close="handleClosePersonMemo"
      @save="handlePersonMemoSave"
    />

    <AddIndustryMemo
      v-if="showAddIndustryMemo && currentUserId"
      :user-id="currentUserId"
      @close="handleCloseIndustryMemo"
      @save="handleIndustryMemoSave"
    />

    <KnowledgeDialog
      v-if="showKnowledgeDialog"
      @close="handleCloseKnowledgeDialog"
    />
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router';
import { onMounted, onUnmounted, ref, onBeforeMount } from 'vue';
import { showSuccessToast } from 'vant';
import { getUserInfo } from '@/apis/common';
import { getUserProfile } from '@/apis/relation';
import { comprehensiveSearchStream } from './apis/search';
import InputSection from './components/InputSection.vue';
import PersonalMemo from './components/PersonalMemo.vue';
import IndustryMemo from './components/IndustryMemo.vue';
import CompanyKnowledgeBase from './components/CompanyKnowledgeBase.vue';
import PreInfo from './components/PreInfo.vue';
import AddPersonMemo from './components/AddPersonMemo.vue';
import AddIndustryMemo from './components/AddIndustryMemo.vue';
import KnowledgeDialog from './components/KnowledgeDialog.vue';
import AIPanel from './components/AIPanel.vue';

const router = useRouter();

// 输入框引用
const inputSectionRef = ref<InstanceType<typeof InputSection> | null>(null);

// AIPanel组件引用
const aiPanel1Ref = ref<InstanceType<typeof AIPanel> | null>(null);
const aiPanel2Ref = ref<InstanceType<typeof AIPanel> | null>(null);
const aiPanel3Ref = ref<InstanceType<typeof AIPanel> | null>(null);

// 用户信息
const currentUserId = ref('');
const currentPersonId = ref('');
const currentUserName = ref('');

// 备忘录相关状态
const showMemoSection = ref(true);
const personalMemoRef = ref<InstanceType<typeof PersonalMemo> | null>(null);
const industryMemoRef = ref<InstanceType<typeof IndustryMemo> | null>(null);

// AIPanel数据状态
interface IThinkingItem {
  type: 'status' | 'question' | 'log' | 'search_questions' | 'search_result' | 'core_answer_start' | 'final_answer_complete';
  message?: string;
  step?: 'start' | 'processing' | 'complete';
  question?: string;
  questions?: string[];
  results?: Array<{ title: string; link: string }>;
}

interface IThinkingData {
  items: IThinkingItem[];
  isLoading: boolean;
}

interface IMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: number;
  thinkingData?: IThinkingData; // 助手消息包含完整的思考过程
}

interface IPanelData {
  messages: IMessage[];
  isLoading: boolean;
  currentThinkingData: IThinkingData;
}

const panel1Data = ref<IPanelData>({
  messages: [],
  isLoading: false,
  currentThinkingData: { items: [], isLoading: false },
});

const panel2Data = ref<IPanelData>({
  messages: [],
  isLoading: false,
  currentThinkingData: { items: [], isLoading: false },
});

const panel3Data = ref<IPanelData>({
  messages: [],
  isLoading: false,
  currentThinkingData: { items: [], isLoading: false },
});

// 对话框状态
const showAddPersonMemo = ref(false);
const showAddIndustryMemo = ref(false);
const showKnowledgeDialog = ref(false);

// 返回上一页
const goBack = () => {
  router.back();
};

// 切换备忘录区域显示
const toggleMemoSection = () => {
  showMemoSection.value = !showMemoSection.value;
  console.log('🔄 [ThreeInOne] 切换备忘录区域显示:', showMemoSection.value);
};

// 生成唯一ID
const generateMessageId = () => {
  return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
};

// 处理发送消息 - 按回车或点击提交按钮时触发，同时向三个AI面板发送消息
const handleSendMessage = async (message: string) => {
  if (!message.trim() || !currentUserId.value) {
    return;
  }

  console.log('🚀 [ThreeInOne] 收到消息，开始向三个AI面板发送:', message);

  // 为每个面板添加用户消息
  const userMessage: IMessage = {
    id: generateMessageId(),
    type: 'user',
    content: message,
    timestamp: Date.now(),
  };

  // 添加用户消息到所有面板
  panel1Data.value.messages.push({ ...userMessage, id: generateMessageId() });
  panel2Data.value.messages.push({ ...userMessage, id: generateMessageId() });
  panel3Data.value.messages.push({ ...userMessage, id: generateMessageId() });

  // 设置加载状态
  panel1Data.value.isLoading = true;
  panel2Data.value.isLoading = true;
  panel3Data.value.isLoading = true;

  // 重置当前思考数据
  panel1Data.value.currentThinkingData = { items: [], isLoading: false };
  panel2Data.value.currentThinkingData = { items: [], isLoading: false };
  panel3Data.value.currentThinkingData = { items: [], isLoading: false };

  // 创建三个独立的搜索请求
  const searchPromises = [
    startSearchForPanel(message, panel1Data, 1),
    startSearchForPanel(message, panel2Data, 2),
    startSearchForPanel(message, panel3Data, 3),
  ];

  // 并行执行三个搜索
  try {
    await Promise.allSettled(searchPromises);
    console.log('✅ [ThreeInOne] 所有搜索完成');
  } catch (error) {
    console.error('❌ [ThreeInOne] 搜索出错:', error);
  }
};

// 处理子输入框消息
const handleSubMessage = async (message: string, panelIndex: number) => {
  if (!message.trim() || !currentUserId.value) {
    return;
  }

  console.log(`🚀 [ThreeInOne] 收到面板${panelIndex}的子输入消息:`, message);

  // 根据面板索引选择对应的数据
  let panelData;
  switch (panelIndex) {
    case 1:
      panelData = panel1Data;
      break;
    case 2:
      panelData = panel2Data;
      break;
    case 3:
      panelData = panel3Data;
      break;
    default:
      return;
  }

  // 如果面板正在加载中，不允许新的请求
  if (panelData.value.isLoading) {
    console.warn(`⚠️ [ThreeInOne] 面板${panelIndex}正在处理中，请等待完成`);
    return;
  }

  // 添加用户消息到对应面板
  const userMessage: IMessage = {
    id: generateMessageId(),
    type: 'user',
    content: message,
    timestamp: Date.now(),
  };

  panelData.value.messages.push(userMessage);
  panelData.value.isLoading = true;
  panelData.value.currentThinkingData = { items: [], isLoading: false };

  // 启动单个面板的搜索
  try {
    await startSearchForPanel(message, panelData, panelIndex);
    console.log(`✅ [ThreeInOne] 面板${panelIndex}搜索完成`);
  } catch (error) {
    console.error(`❌ [ThreeInOne] 面板${panelIndex}搜索出错:`, error);
  }
};

// 处理批量搜索
const handleBatchSearch = async (message: string) => {
  if (!message.trim() || !currentUserId.value) {
    return;
  }

  console.log('🚀 [ThreeInOne] 开始批量搜索:', message);

  // 为每个面板添加用户消息
  const userMessage: IMessage = {
    id: generateMessageId(),
    type: 'user',
    content: message,
    timestamp: Date.now(),
  };

  // 添加用户消息到所有面板
  panel1Data.value.messages.push({ ...userMessage, id: generateMessageId() });
  panel2Data.value.messages.push({ ...userMessage, id: generateMessageId() });
  panel3Data.value.messages.push({ ...userMessage, id: generateMessageId() });

  // 设置加载状态
  panel1Data.value.isLoading = true;
  panel2Data.value.isLoading = true;
  panel3Data.value.isLoading = true;

  // 重置当前思考数据
  panel1Data.value.currentThinkingData = { items: [], isLoading: false };
  panel2Data.value.currentThinkingData = { items: [], isLoading: false };
  panel3Data.value.currentThinkingData = { items: [], isLoading: false };

  // 创建三个独立的搜索请求
  const searchPromises = [
    startSearchForPanel(message, panel1Data, 1),
    startSearchForPanel(message, panel2Data, 2),
    startSearchForPanel(message, panel3Data, 3),
  ];

  // 并行执行三个搜索
  try {
    await Promise.allSettled(searchPromises);
    console.log('✅ [ThreeInOne] 所有批量搜索完成');
  } catch (error) {
    console.error('❌ [ThreeInOne] 批量搜索出错:', error);
  }
};

// 为单个面板启动搜索
const startSearchForPanel = async (message: string, panelData: any, panelIndex: number) => {
  console.log(`🔍 [ThreeInOne] 开始为面板${panelIndex}搜索:`, message);

  // 创建AbortController用于取消请求
  const controller = new AbortController();

  try {
    await comprehensiveSearchStream(
      {
        question: message,
        user_id: currentUserId.value,
        conversation_id: `search_chat_panel_${panelIndex}`,
      },
      {
        onStatus: (data) => {
          console.log(`📊 [ThreeInOne] 面板${panelIndex} 状态:`, data);
          const newThinkingData = {
            ...panelData.value.currentThinkingData,
            items: [
              ...panelData.value.currentThinkingData.items,
              {
                type: 'status' as const,
                message: data.message,
                step: data.step,
              }
            ]
          };
          panelData.value.currentThinkingData = newThinkingData;
        },
        onQuestion: (data) => {
          console.log(`❓ [ThreeInOne] 面板${panelIndex} 问题:`, data);
          const newThinkingData = {
            ...panelData.value.currentThinkingData,
            items: [
              ...panelData.value.currentThinkingData.items,
              {
                type: 'question' as const,
                question: data.question,
              }
            ]
          };
          panelData.value.currentThinkingData = newThinkingData;
        },
        onLog: (data) => {
          console.log(`📝 [ThreeInOne] 面板${panelIndex} 日志:`, data);
          const newThinkingData = {
            ...panelData.value.currentThinkingData,
            items: [
              ...panelData.value.currentThinkingData.items,
              {
                type: 'log' as const,
                message: data.message,
              }
            ]
          };
          panelData.value.currentThinkingData = newThinkingData;
        },
        onKnowledgeContext: (data) => {
          console.log(`📚 [ThreeInOne] 面板${panelIndex} 知识上下文:`, data);
          // 可以根据需要处理知识上下文
        },
        onSearchQuestions: (data) => {
          console.log(`🔍 [ThreeInOne] 面板${panelIndex} 搜索问题:`, data);
          const newThinkingData = {
            ...panelData.value.currentThinkingData,
            items: [
              ...panelData.value.currentThinkingData.items,
              {
                type: 'search_questions' as const,
                questions: data.questions,
              }
            ]
          };
          panelData.value.currentThinkingData = newThinkingData;
        },
        onSearchResult: (data) => {
          console.log(`📋 [ThreeInOne] 面板${panelIndex} 搜索结果:`, data);
          const results = data.results.map(result => ({
            title: result.title,
            link: result.link,
          }));
          const newThinkingData = {
            ...panelData.value.currentThinkingData,
            items: [
              ...panelData.value.currentThinkingData.items,
              {
                type: 'search_result' as const,
                results,
              }
            ]
          };
          panelData.value.currentThinkingData = newThinkingData;
        },
        onCoreAnswerStart: (data) => {
          console.log(`🎯 [ThreeInOne] 面板${panelIndex} 核心答案开始:`, data);
          const newThinkingData = {
            ...panelData.value.currentThinkingData,
            items: [
              ...panelData.value.currentThinkingData.items,
              {
                type: 'core_answer_start' as const,
                message: '开始生成核心答案',
              }
            ]
          };
          panelData.value.currentThinkingData = newThinkingData;
        },
        onFinalAnswer: (data) => {
          console.log(`✅ [ThreeInOne] 面板${panelIndex} 最终答案:`, data);

          // 添加最终答案完成标记到思考数据
          const newThinkingData = {
            ...panelData.value.currentThinkingData,
            items: [
              ...panelData.value.currentThinkingData.items,
              {
                type: 'final_answer_complete' as const,
                message: '答案生成完成',
              }
            ],
            isLoading: false
          };
          panelData.value.currentThinkingData = newThinkingData;

          // 构建完整答案
          const coreAnswer = data.core_answer || '';
          const detailedAnswer = data.detailed_answer || '';
          const fullAnswer = [coreAnswer, detailedAnswer].filter(Boolean).join('\n\n');

          // 创建助手消息并添加到消息列表，包含完整的思考过程
          const assistantMessage: IMessage = {
            id: generateMessageId(),
            type: 'assistant',
            content: fullAnswer,
            timestamp: Date.now(),
            thinkingData: newThinkingData, // 保存完整的思考过程
          };

          panelData.value.messages.push(assistantMessage);
          panelData.value.isLoading = false;

          // 清空当前思考数据，准备下一次交互
          panelData.value.currentThinkingData = { items: [], isLoading: false };
        },
        onError: (error) => {
          console.error(`❌ [ThreeInOne] 面板${panelIndex} 搜索错误:`, error);
          panelData.value.isLoading = false;
        },
        onClose: () => {
          console.log(`🏁 [ThreeInOne] 面板${panelIndex} 搜索连接关闭`);
        },
      },
      controller.signal,
    );
  } catch (error) {
    console.error(`❌ [ThreeInOne] 面板${panelIndex} 搜索失败:`, error);
    panelData.value.isLoading = false;
  }
};

// 备忘录相关处理函数
const handleAddPersonMemo = () => {
  console.log('🔄 [ThreeInOne] 打开添加个人备忘录对话框');
  showAddPersonMemo.value = true;
};

const handleEditPersonMemo = (memo: any) => {
  console.log('🔄 [ThreeInOne] 编辑个人备忘录:', memo);
  // TODO: 实现编辑逻辑
};

const handleDeleteMemo = (memo: any) => {
  console.log('🔄 [ThreeInOne] 删除备忘录:', memo);
  // TODO: 实现删除逻辑
};

const handleAddIndustryMemo = () => {
  console.log('🔄 [ThreeInOne] 打开添加行业备忘录对话框');
  showAddIndustryMemo.value = true;
};

const handleEditIndustryMemo = (memo: any) => {
  console.log('🔄 [ThreeInOne] 编辑行业备忘录:', memo);
  // TODO: 实现编辑逻辑
};

const handleDeleteIndustryMemo = (memo: any) => {
  console.log('🔄 [ThreeInOne] 删除行业备忘录:', memo);
  // TODO: 实现删除逻辑
};

const handleOpenKnowledgeDialog = () => {
  console.log('🔄 [ThreeInOne] 打开知识库对话框');
  showKnowledgeDialog.value = true;
};

const handleAddPreInfo = () => {
  console.log('🔄 [ThreeInOne] 预获取资讯按钮被点击');
  // TODO: 实现预获取资讯功能
};

// 对话框关闭处理
const handleClosePersonMemo = () => {
  showAddPersonMemo.value = false;
};

const handleCloseIndustryMemo = () => {
  showAddIndustryMemo.value = false;
};

const handleCloseKnowledgeDialog = () => {
  showKnowledgeDialog.value = false;
};

// 备忘录保存处理
const handlePersonMemoSave = (content: string) => {
  console.log('🔄 [ThreeInOne] 个人备忘录保存成功:', content);
  showAddPersonMemo.value = false;
  if (personalMemoRef.value) {
    personalMemoRef.value.fetchMemories();
  }
};

const handleIndustryMemoSave = (content: string) => {
  console.log('🔄 [ThreeInOne] 行业备忘录保存成功:', content);
  showAddIndustryMemo.value = false;
  if (industryMemoRef.value) {
    industryMemoRef.value.fetchIndustryMemos();
  }
};

// 添加全屏样式
const addFullscreenStyles = () => {
  document.documentElement.classList.add('agentpk-active');
  document.body.classList.add('agentpk-active');
  const app = document.getElementById('app');
  if (app) {
    app.classList.add('agentpk-active');
  }
};

// 移除全屏样式
const removeFullscreenStyles = () => {
  document.documentElement.classList.remove('agentpk-active');
  document.body.classList.remove('agentpk-active');
  const app = document.getElementById('app');
  if (app) {
    app.classList.remove('agentpk-active');
  }
};

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    console.log('🔄 [ThreeInOne] 开始获取用户信息...');
    const userInfo = await getUserInfo();
    console.log('📡 [ThreeInOne] 用户信息:', userInfo);

    if (userInfo && userInfo.login) {
      currentUserId.value = userInfo.login;
      currentUserName.value = userInfo.name;
      console.log('✅ [ThreeInOne] 用户信息获取成功:', userInfo);
    } else {
      console.warn('⚠️ [ThreeInOne] 用户信息格式异常');
      currentUserId.value = 'unknown_user';
    }
  } catch (error) {
    console.error('❌ [ThreeInOne] 获取用户信息失败:', error);
    currentUserId.value = 'unknown_user';
  }
};

// 获取用户档案信息
const fetchUserProfile = async () => {
  try {
    if (!currentUserId.value) return;

    const profile = await getUserProfile({ user_id: currentUserId.value });
    if (profile.result === 'success' && profile.person) {
      currentPersonId.value = profile.person.person_id;
      console.log('✅ [ThreeInOne] 用户档案信息获取成功:', profile);

      // PersonalMemo组件会自动监听props变化并获取数据，无需手动调用
    }
  } catch (error) {
    console.error('❌ [ThreeInOne] 获取用户档案信息失败:', error);
  }
};



// 组件挂载前的准备工作
onBeforeMount(() => {
  console.log('🔄 [ThreeInOne] 页面即将挂载，添加全屏样式');
  addFullscreenStyles();
});

// 组件挂载时的初始化工作
onMounted(async () => {
  console.log('🔄 [ThreeInOne] 页面挂载，开始初始化');

  // 获取用户信息
  await fetchUserInfo();

  // 获取用户档案信息
  await fetchUserProfile();

  // 检查是否有查询参数，如果有则设置到输入框中，但不自动执行搜索
  const query = router.currentRoute.value.query.q as string;
  if (query && query.trim() && inputSectionRef.value) {
    console.log('🔍 [ThreeInOne] 检测到查询参数，设置到输入框:', query);
    inputSectionRef.value.setInputValue(query.trim());
  }

  console.log('✅ [ThreeInOne] 页面初始化完成');
});

// 组件卸载时的清理工作
onUnmounted(() => {
  console.log('🔚 [ThreeInOne] 页面卸载，恢复原始样式');
  removeFullscreenStyles();
  console.log('✅ [ThreeInOne] 组件清理完成');
});
</script>

<style lang="scss" scoped>
.three-in-one-fullscreen {
  // 占满整个浏览器窗口，不受H5布局限制
  position: relative;
  top: 0;
  left: 0;
  height: 100vh;
  width: 100%;
  max-width: 100%;
  background: linear-gradient(135deg, #ffffff 0%, #faf9ff 50%, #f8f6ff 100%);
  z-index: 9999;
  overflow: hidden; // 禁止整体滚动
  display: flex;
  flex-direction: column;

  // 确保不受父容器样式影响
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.top-bar {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(139, 126, 216, 0.2);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 10;
  flex-shrink: 0; // 防止被压缩

  // 董会答介绍区域
  .assistant-intro {
    display: flex;
    align-items: center;
    gap: 6px;
    width: 100%;
    background: #f3f9ff; // 更浅的蓝色背景
    padding: 6px;

    .assistant-avatar {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      overflow: hidden;
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .assistant-info {
      flex: 1;

      .assistant-name {
        font-size: 28px;
        font-weight: 600;
        color: hsl(238, 57%, 25%);
      }

      .assistant-desc {
        font-size: 22px;
        color: #333;
      }
    }

    .assistant-actions {
      display: flex;
      align-items: center;
      gap: 6px;

      .user-greeting {
        font-size: 24px;
        font-weight: 500;
        color: hsl(238, 57%, 25%);
        margin-right: 8px;
      }

      .action-btn {
        padding: 8px 8px;
        border: none;
        color: hsl(238, 57%, 25%);
        font-size: 24px;
        background-color: transparent;
        cursor: pointer;
        transition: all 0.3s ease;

        &.back-btn {
          display: flex;
          align-items: center;
          gap: 8px;
          background: rgba(139, 126, 216, 0.1);
          border: 1px solid rgba(139, 126, 216, 0.3);
          border-radius: 8px;
          font-weight: 500;
          backdrop-filter: blur(10px);

          &:hover {
            background: rgba(139, 126, 216, 0.2);
            border-color: rgba(139, 126, 216, 0.5);
            transform: translateY(-1px);
          }

          &:active {
            transform: translateY(0);
          }

          svg {
            width: 24px;
            height: 24px;
          }
        }
      }
    }
  }

  // 备忘录组件区域 - 上下排布
  .memo-section {
    display: flex;
    flex-direction: column;
    width: 100%;
  }
}

.main-content {
  position: relative;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  width: 100%;
  padding: 0 20px 120px 20px; // 底部留出空间给固定的input-section
  flex: 1; // 占满剩余空间
  min-height: 0; // 确保flex子元素可以正确计算高度
  box-sizing: border-box;

  // 动态调整 padding-top，根据备忘录区域是否展开
  padding-top: 0px; // 基础高度（董会答区域）

  // 当备忘录展开时，增加额外的空间
  &.memo-expanded {
    padding-top: 0px; // 展开时的总高度（增加了获取预资讯组件）
  }

  .ai-comparison-section {
    flex: 1;
    display: flex;
    justify-content: center;
    gap: 20px;
    width: 80vw;
    margin: 0 auto;
    padding: 20px 0;
    min-height: 500px;
    align-items: stretch;

    // 确保三个AIPanel等宽
    > * {
      flex: 1;
      min-width: 0; // 防止内容溢出
    }
  }
}

.input-section {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  padding: 20px 40px;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
}
</style>

<style>
/* 全局样式，确保页面真正占满全屏 */
body.agentpk-active {
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
}

html.agentpk-active {
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
}

#app.agentpk-active {
  overflow: hidden !important;
}

/* 响应式适配 - 确保在不同屏幕尺寸下正常显示 */
@media (max-width: 768px) {
  .three-in-one-fullscreen {
    width: 100% !important;
    max-width: 100% !important;
    min-width: 320px !important;
  }

  .main-content {
    padding: 0 20px 120px 20px !important; /* 底部留出空间给固定的input-section */
    max-width: 100% !important;

    &.memo-expanded {
      padding-top: 0px !important; /* 减少顶部padding */
    }

    &:not(.memo-expanded) {
      padding-top: 0px !important; /* 减少顶部padding */
    }
  }

  .top-bar {
    padding: 0px !important;
  }

  .assistant-intro {
    padding: 2px !important;
    gap: 4px !important;
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;

    .assistant-avatar {
      width: 45px !important;
      height: 45px !important;
      flex-shrink: 0 !important;
    }

    .assistant-info {
      flex: 1 !important;
      min-width: 0 !important;

      .assistant-name {
        font-size: 16px !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
      }

      .assistant-desc {
        font-size: 12px !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
      }
    }

    .assistant-actions {
      gap: 3px !important;
      flex-shrink: 0 !important;
      display: flex !important;
      flex-direction: row !important;
      align-items: center !important;
      flex-wrap: nowrap !important;

      .user-greeting {
        font-size: 12px !important;
        margin-right: 2px !important;
        white-space: nowrap !important;
      }

      .action-btn {
        font-size: 12px !important;
        padding: 2px 4px !important;
        border-radius: 6px !important;
        background: transparent !important;
        border: none !important;
        white-space: nowrap !important;
        min-width: auto !important;
      }
    }
  }

  .input-section {
    padding: 20px !important;

    /* 确保输入组件在移动端正常显示 */
    :deep(.input-container) {
      max-width: 100% !important;
    }

    :deep(.input-wrapper) {
      padding: 0 16px !important;
    }
  }

  /* 备忘录区域的响应式适配 */
  .memo-section {
    gap: 8px !important;
  }

  .ai-comparison-section {
    flex-direction: column !important;
    gap: 12px !important;
    min-height: 300px !important;

    > * {
      flex: none !important;
      height: 200px !important;
    }
  }
}
</style>
